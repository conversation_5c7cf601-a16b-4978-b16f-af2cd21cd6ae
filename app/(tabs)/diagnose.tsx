import React, { useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, ScrollView, TextInput, Platform, KeyboardAvoidingView, Alert } from 'react-native';
import { Stack } from 'expo-router';
import { Stethoscope, Leaf, Info, MapPin, FileText } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { BloomSnapCamera } from '@/components/camera/CameraView';
import { PlantDetailsDisplay } from '@/components/identification/PlantDetailsDisplay';
import { DiagnosisResultDisplay } from '@/components/diagnosis/DiagnosisResultDisplay';
import { ProgressIndicator } from '@/components/ui/ProgressIndicator';
import { useIdentification } from '@/hooks/useIdentificationStore';
import { useGarden } from '@/hooks/useGardenStore';
import { Plant } from '@/types/plant';

export default function DiagnoseScreen() {
  const [showCamera, setShowCamera] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [problemDescription, setProblemDescription] = useState('');
  const [notes, setNotes] = useState('');
  const [location, setLocation] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const { diagnosePlantForDiagnose, diagnoseResult, clearDiagnoseResult, isDiagnosing, recentDiagnoses, isLoadingRecentDiagnoses } = useIdentification();
  const { addPlant, addPlantAndShare, shareDiagnosisOnly } = useGarden();

  const handleCapture = async (uri: string) => {
    setShowCamera(false);
    setCapturedImage(uri);
    setShowPreview(true);
  };

  const handleDiagnose = async () => {
    if (capturedImage) {
      setShowPreview(false);
      // Always use diagnosis mode, even if problemDescription is empty
      await diagnosePlantForDiagnose(capturedImage, problemDescription, location);
    }
  };

  const handleAddToGarden = async (plant: Plant) => {
    if (isSaving) return; // Prevent multiple saves

    setIsSaving(true);
    try {
      // Pass the diagnosisId if this result came from a diagnosis
      const diagnosisId = diagnoseResult?.diagnosisId;
      await addPlant(plant, plant.commonName, notes, diagnosisId, location);
      clearPreviewData();

      // Haptic feedback for mobile platforms
      if (Platform.OS !== 'web') {
        try {
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        } catch (error) {
          console.log('Haptic feedback not available:', error);
        }
      }

      // Cross-platform alert confirmation
      Alert.alert(
        'Plant Saved!',
        'Your plant has been saved to your garden.',
        [{ text: 'OK' }],
        { cancelable: true }
      );
    } catch (error) {
      console.error('Error saving plant:', error);
      Alert.alert(
        'Error',
        'Failed to save plant to your garden. Please try again.',
        [{ text: 'OK' }],
        { cancelable: true }
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddAndShare = async (plant: Plant) => {
    if (isSaving) return; // Prevent multiple saves

    setIsSaving(true);
    try {
      // Add to garden with is_public = true and save diagnosis with is_public = true
      const diagnosisId = diagnoseResult?.diagnosisId;
      await addPlantAndShare(plant, plant.commonName, notes, diagnosisId, location);
      clearPreviewData();

      // Haptic feedback for mobile platforms
      if (Platform.OS !== 'web') {
        try {
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        } catch (error) {
          console.log('Haptic feedback not available:', error);
        }
      }

      // Cross-platform alert confirmation
      Alert.alert(
        'Plant Saved & Shared!',
        'Your plant has been saved to your garden and shared with the community.',
        [{ text: 'OK' }],
        { cancelable: true }
      );
    } catch (error) {
      console.error('Error saving and sharing plant:', error);
      Alert.alert(
        'Error',
        'Failed to save and share plant. Please try again.',
        [{ text: 'OK' }],
        { cancelable: true }
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleNewScan = () => {
    clearDiagnoseResult();
    clearPreviewData();
    setShowCamera(true);
  };

  const clearPreviewData = () => {
    setCapturedImage(null);
    setProblemDescription('');
    setNotes('');
    setLocation('');
    setShowPreview(false);
    setIsSaving(false); // Reset saving state
  };

  const startCamera = () => {
    clearDiagnoseResult();
    clearPreviewData();
    setShowCamera(true);
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const handleRetakePhoto = () => {
    setCapturedImage(null);
    setShowPreview(false);
    setShowCamera(true);
  };

  if (showCamera) {
    return <BloomSnapCamera onCapture={handleCapture} onCancel={() => setShowCamera(false)} mode="diagnose" />;
  }

  if (isDiagnosing) {
    return <ProgressIndicator mode="diagnose" />;
  }

  if (showPreview && capturedImage) {
    return (
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        testID="preview-screen"
      >
        <Stack.Screen options={{ title: 'Plant Diagnosis Preview' }} />

        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.previewContainer}>
            <Image source={{ uri: capturedImage }} style={styles.previewImage} />
            
            <View style={styles.previewContent}>
              <Text style={styles.previewTitle}>Describe the Problem</Text>
              <Text style={styles.previewSubtitle}>
                Help our AI diagnose your plant by describing what you&apos;ve noticed
              </Text>

              <View style={styles.problemInputContainer}>
                <Text style={styles.problemInputLabel}>What&apos;s wrong with your plant? (optional)</Text>
                <TextInput
                  style={styles.problemInput}
                  placeholder="e.g., mushy leaves, brown spots, wilting, yellowing..."
                  placeholderTextColor={Colors.textMuted}
                  value={problemDescription}
                  onChangeText={setProblemDescription}
                  multiline
                  numberOfLines={4}
                  testID="problem-description-input"
                />
              </View>



              <View style={styles.previewActions}>
                <Button
                  title="Diagnose Plant"
                  onPress={handleDiagnose}
                  style={styles.diagnoseButton}
                  loading={isDiagnosing}
                  testID="diagnose-button"
                />
                <Button
                  title="Retake Photo"
                  variant="outline"
                  onPress={handleRetakePhoto}
                  style={styles.retakeButton}
                  testID="retake-button"
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    );
  }

  if (diagnoseResult) {
    return (
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        testID="result-screen"
      >
        <Stack.Screen options={{ title: 'Plant Diagnosed' }} />

        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.resultContainer}>
            {/* Plant Image with improved styling */}
            <View style={styles.imageContainer}>
              <Image source={{ uri: diagnoseResult.imageUri }} style={styles.resultImage} />
            </View>

            <View style={styles.resultContent}>
              <PlantDetailsDisplay
                plant={diagnoseResult.plant}
                confidence={diagnoseResult.confidence}
                identificationData={diagnoseResult.diagnosisData}
                isDiagnosis={true}
              />

              {/* Diagnosis Information - New Card-based Display */}
              {diagnoseResult.diagnosisData && (
                <DiagnosisResultDisplay diagnosisData={diagnoseResult.diagnosisData} />
              )}

              {/* Notes Section */}
              <View style={styles.inputSection}>
                <View style={styles.inputHeader}>
                  <FileText size={20} color={Colors.primary} />
                  <Text style={styles.inputLabel}>Notes (Optional)</Text>
                </View>
                <TextInput
                  style={styles.textInput}
                  placeholder="Add any notes about this plant..."
                  value={notes}
                  onChangeText={setNotes}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>

              {/* Location Section */}
              <View style={styles.inputSection}>
                <View style={styles.inputHeader}>
                  <MapPin size={20} color={Colors.primary} />
                  <Text style={styles.inputLabel}>Location (Optional)</Text>
                </View>
                <TextInput
                  style={styles.textInput}
                  placeholder="e.g., Seen along the front garden"
                  value={location}
                  onChangeText={setLocation}
                />
              </View>

              {/* Action Buttons - 3 rows */}
              <View style={styles.actionButtonsContainer}>
                <View style={styles.resultActions}>
                  <Button
                    title={isSaving ? "Saving..." : "Add to my Garden & Showcase this Discovery"}
                    onPress={() => handleAddAndShare(diagnoseResult.plant)}
                    style={styles.primaryActionButton}
                    textStyle={styles.primaryActionText}
                    testID="add-and-share-button"
                    disabled={isSaving}
                    loading={isSaving}
                  />
                  <Button
                    title={isSaving ? "Saving..." : "No! Keep this to myself"}
                    variant="outline"
                    onPress={() => handleAddToGarden(diagnoseResult.plant)}
                    style={styles.secondaryActionButton}
                    testID="add-to-garden-button"
                    disabled={isSaving}
                    loading={isSaving}
                  />
                  <Button
                    title="Cancel"
                    variant="outline"
                    onPress={handleNewScan}
                    style={styles.secondaryActionButton}
                    testID="new-scan-button"
                  />
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    );
  }

  return (
    <View style={styles.container} testID="diagnose-screen">
      <Stack.Screen options={{ title: 'Diagnose' }} />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Image
            source={{ uri: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80' }}
            style={styles.headerImage}
          />
          <View style={styles.headerContent}>
            <Text style={styles.title}>Diagnose & share plant health solutions</Text>
            <Text style={styles.subtitle}>
              Get AI diagnosis and connect with community experts for treatment recommendations
            </Text>
          </View>
        </View>

        <View style={styles.actionsContainer}>
          <Button
            title="Start Plant Diagnosis"
            onPress={startCamera}
            style={styles.snapButton}
            textStyle={styles.snapButtonText}
            testID="start-camera-button"
          />
          
          <View style={styles.featureCards}>
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.secondary }]}>
                <Stethoscope size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>AI Diagnosis</Text>
              <Text style={styles.featureText}>Advanced AI analyzes plant health</Text>
            </Card>
            
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.tertiary }]}>
                <Leaf size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>Treatment Plans</Text>
              <Text style={styles.featureText}>Get personalized care recommendations</Text>
            </Card>
            
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.accent1 }]}>
                <Info size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>Expert Tips</Text>
              <Text style={styles.featureText}>Learn from plant care experts</Text>
            </Card>
          </View>
        </View>

        <View style={styles.recentContainer}>
          <Text style={styles.sectionTitle}>Recently Diagnosed</Text>

          {isLoadingRecentDiagnoses ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading recent diagnoses...</Text>
            </View>
          ) : recentDiagnoses.length > 0 ? (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.recentScroll}>
              {recentDiagnoses.map((diagnosis) => (
                <TouchableOpacity key={diagnosis.id} style={styles.recentItem} testID={`recent-diagnosis-${diagnosis.id}`}>
                  <Image source={{ uri: diagnosis.image_url }} style={styles.recentImage} />
                  <Text style={styles.recentName}>{diagnosis.diagnosed_problem || 'Plant Issue'}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.emptyRecentContainer}>
              <Text style={styles.emptyRecentText}>No recent diagnoses</Text>
              <Text style={styles.emptyRecentSubtext}>Start diagnosing plants to see them here</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  header: {
    position: 'relative',
    height: 200,
    marginBottom: 20,
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  headerContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.background,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.background,
    opacity: 0.9,
  },
  actionsContainer: {
    padding: 20,
  },
  snapButton: {
    height: 60,
    borderRadius: 30,
    marginBottom: 24,
  },
  snapButtonText: {
    fontSize: 18,
  },
  featureCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  featureCard: {
    flex: 1,
    marginHorizontal: 4,
    padding: 12,
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  featureText: {
    fontSize: 12,
    color: Colors.textLight,
    textAlign: 'center',
  },
  recentContainer: {
    padding: 20,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  recentScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  recentItem: {
    width: 120,
    marginRight: 16,
  },
  recentImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
    marginBottom: 8,
  },
  recentName: {
    fontSize: 14,
    color: Colors.text,
    textAlign: 'center',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textMuted,
  },
  emptyRecentContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyRecentText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textMuted,
    marginBottom: 4,
  },
  emptyRecentSubtext: {
    fontSize: 14,
    color: Colors.textLight,
    textAlign: 'center',
  },
  previewContainer: {
    flex: 1,
  },
  previewImage: {
    width: '100%',
    height: 300,
    resizeMode: 'contain',
    backgroundColor: Colors.cardBackground,
    borderRadius: 12,
  },
  previewContent: {
    padding: 20,
  },
  previewTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 8,
  },
  previewSubtitle: {
    fontSize: 16,
    color: Colors.textMuted,
    marginBottom: 24,
  },
  problemInputContainer: {
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  problemInputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  problemInput: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.text,
    textAlignVertical: 'top',
    minHeight: 100,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.text,
    backgroundColor: Colors.cardBackground,
    minHeight: 50,
  },
  previewActions: {
    gap: 12,
    marginTop: 8,
  },
  retakeButton: {
    marginTop: 8,
  },
  diagnoseButton: {
    height: 56,
    borderRadius: 12,
  },
  resultContainer: {
    flex: 1,
  },
  imageContainer: {
    padding: 16,
    backgroundColor: Colors.background,
  },
  resultImage: {
    width: '100%',
    height: 280,
    resizeMode: 'cover',
    backgroundColor: Colors.cardBackground,
    borderRadius: 16,
  },
  resultContent: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    paddingTop: 20,
  },
  resultTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  resultScientificName: {
    fontSize: 18,
    fontStyle: 'italic',
    color: Colors.textLight,
    marginBottom: 16,
  },
  diagnosisSection: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: Colors.cardBackground,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.error,
  },
  diagnosisTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  diagnosisText: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
  },
  actionItem: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 4,
  },
  actionButtonsContainer: {
    backgroundColor: Colors.background,
    padding: 20,
    paddingTop: 24,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 16,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  resultActions: {
    gap: 12,
  },
  primaryActionButton: {
    height: 56,
    borderRadius: 12,
    backgroundColor: Colors.primary,
  },
  primaryActionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryActionButton: {
    height: 48,
    borderRadius: 12,
  },
  // New comprehensive plant display styles
  plantHeader: {
    marginBottom: 20,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  confidenceLabel: {
    fontSize: 14,
    color: Colors.textMuted,
  },
  confidenceValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary,
  },
  descriptionSection: {
    marginBottom: 24,
  },
  resultDescription: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
  },
  careSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  careGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  careItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: '45%',
    marginBottom: 8,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginRight: 4,
  },
  careValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
  soilContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  fertilizerContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  toxicityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.errorLight,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  toxicityLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.error,
    marginRight: 4,
  },
  toxicityValue: {
    fontSize: 14,
    color: Colors.error,
    textTransform: 'capitalize',
  },
  tagsSection: {
    marginBottom: 24,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
  },
});