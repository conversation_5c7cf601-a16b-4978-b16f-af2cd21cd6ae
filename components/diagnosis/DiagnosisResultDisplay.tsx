import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {
  ChevronDown,
  ChevronUp,
  AlertTriangle,
  Activity,
  Clock,
  TrendingUp,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap,
  Heart,
  Shield,
  Target,
  List,
  Lightbulb,
} from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { DiagnosisData } from '@/services/openrouter';

interface DiagnosisResultDisplayProps {
  diagnosisData: DiagnosisData;
}

export const DiagnosisResultDisplay: React.FC<DiagnosisResultDisplayProps> = ({
  diagnosisData,
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'mild':
      case 'low':
        return Colors.success;
      case 'moderate':
      case 'medium':
        return Colors.accent1;
      case 'severe':
      case 'high':
      case 'critical':
        return Colors.error;
      default:
        return Colors.textMuted;
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'mild':
      case 'low':
        return CheckCircle;
      case 'moderate':
      case 'medium':
        return AlertCircle;
      case 'severe':
      case 'high':
      case 'critical':
        return XCircle;
      default:
        return AlertTriangle;
    }
  };

  const renderExpandableSection = (
    title: string,
    content: React.ReactNode,
    sectionKey: string,
    icon: React.ComponentType<any>,
    defaultExpanded: boolean = false
  ) => {
    const isExpanded = expandedSections[sectionKey] ?? defaultExpanded;
    const IconComponent = icon;

    return (
      <Card style={styles.sectionCard}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(sectionKey)}
        >
          <View style={styles.sectionTitleContainer}>
            <IconComponent size={20} color={Colors.primary} />
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
          {isExpanded ? (
            <ChevronUp size={20} color={Colors.textMuted} />
          ) : (
            <ChevronDown size={20} color={Colors.textMuted} />
          )}
        </TouchableOpacity>
        {isExpanded && <View style={styles.sectionContent}>{content}</View>}
      </Card>
    );
  };

  const renderDiagnosedProblem = () => {
    if (!diagnosisData.diagnosedProblem) return null;

    const SeverityIcon = getSeverityIcon(diagnosisData.severity);
    const severityColor = getSeverityColor(diagnosisData.severity);

    return (
      <Card style={[styles.problemCard, { borderLeftColor: severityColor }]}>
        <View style={styles.problemHeader}>
          <View style={styles.problemTitleContainer}>
            <AlertTriangle size={24} color={severityColor} />
            <Text style={styles.problemTitle}>Diagnosed Problem</Text>
          </View>
          {diagnosisData.severity && (
            <View style={[styles.severityBadge, { backgroundColor: severityColor }]}>
              <SeverityIcon size={16} color={Colors.background} />
              <Text style={styles.severityText}>
                {diagnosisData.severity.charAt(0).toUpperCase() + diagnosisData.severity.slice(1)}
              </Text>
            </View>
          )}
        </View>
        <Text style={styles.problemDescription}>{diagnosisData.diagnosedProblem}</Text>
        
        {diagnosisData.symptomsObserved && (
          <View style={styles.symptomsContainer}>
            <Text style={styles.symptomsLabel}>Symptoms Observed:</Text>
            <Text style={styles.symptomsText}>{diagnosisData.symptomsObserved}</Text>
          </View>
        )}
      </Card>
    );
  };

  const renderActionsList = (actions: string[], title: string, icon: React.ComponentType<any>) => {
    if (!actions || actions.length === 0) return null;

    const IconComponent = icon;

    return (
      <View>
        {actions.map((action, index) => (
          <View key={index} style={styles.actionItem}>
            <View style={styles.actionNumber}>
              <Text style={styles.actionNumberText}>{index + 1}</Text>
            </View>
            <Text style={styles.actionText}>{action}</Text>
          </View>
        ))}
      </View>
    );
  };

  const renderImmediateActions = () => {
    if (!diagnosisData.immediateActions || diagnosisData.immediateActions.length === 0) return null;

    return renderExpandableSection(
      'Immediate Actions',
      renderActionsList(diagnosisData.immediateActions, 'Immediate Actions', Zap),
      'immediateActions',
      Zap,
      true
    );
  };

  const renderLongTermCare = () => {
    if (!diagnosisData.longTermCare || diagnosisData.longTermCare.length === 0) return null;

    return renderExpandableSection(
      'Long-term Care',
      renderActionsList(diagnosisData.longTermCare, 'Long-term Care', Heart),
      'longTermCare',
      Heart
    );
  };

  const renderPrognosis = () => {
    if (!diagnosisData.prognosis) return null;

    return renderExpandableSection(
      'Prognosis',
      <Text style={styles.prognosisText}>{diagnosisData.prognosis}</Text>,
      'prognosis',
      TrendingUp,
      true
    );
  };

  const renderStepByStepInstructions = () => {
    if (!diagnosisData.stepByStepInstructions || diagnosisData.stepByStepInstructions.length === 0) return null;

    return renderExpandableSection(
      'Step-by-Step Treatment',
      renderActionsList(diagnosisData.stepByStepInstructions, 'Treatment Steps', Target),
      'stepByStep',
      Target
    );
  };

  const renderPreventionTips = () => {
    if (!diagnosisData.preventionTips || diagnosisData.preventionTips.length === 0) return null;

    return renderExpandableSection(
      'Prevention Tips',
      renderActionsList(diagnosisData.preventionTips, 'Prevention Tips', Shield),
      'prevention',
      Shield
    );
  };

  const renderProductRecommendations = () => {
    if (!diagnosisData.productRecommendations || diagnosisData.productRecommendations.length === 0) return null;

    return renderExpandableSection(
      'Product Recommendations',
      <View>
        {diagnosisData.productRecommendations.map((product, index) => (
          <View key={index} style={styles.productItem}>
            <View style={styles.productBullet} />
            <Text style={styles.productText}>{product}</Text>
          </View>
        ))}
      </View>,
      'products',
      Lightbulb
    );
  };

  const renderLikelyCauses = () => {
    if (!diagnosisData.likelyCauses || diagnosisData.likelyCauses.length === 0) return null;

    return renderExpandableSection(
      'Likely Causes',
      <View>
        {diagnosisData.likelyCauses.map((cause, index) => (
          <View key={index} style={styles.causeItem}>
            <View style={styles.causeBullet} />
            <Text style={styles.causeText}>{cause}</Text>
          </View>
        ))}
      </View>,
      'causes',
      List
    );
  };

  return (
    <View style={styles.container}>
      {renderDiagnosedProblem()}
      {renderImmediateActions()}
      {renderPrognosis()}
      {renderLongTermCare()}
      {renderStepByStepInstructions()}
      {renderLikelyCauses()}
      {renderPreventionTips()}
      {renderProductRecommendations()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  problemCard: {
    marginBottom: 16,
    borderLeftWidth: 4,
    backgroundColor: '#FFF8F0',
  },
  problemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  problemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  problemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginLeft: 8,
  },
  severityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  severityText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.background,
    marginLeft: 4,
  },
  problemDescription: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 12,
  },
  symptomsContainer: {
    backgroundColor: Colors.secondary,
    padding: 12,
    borderRadius: 8,
  },
  symptomsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginBottom: 4,
  },
  symptomsText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  sectionCard: {
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  sectionContent: {
    paddingTop: 12,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  actionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  actionNumberText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.background,
  },
  actionText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    flex: 1,
  },
  prognosisText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  productBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary,
    marginRight: 12,
    marginTop: 7,
  },
  productText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    flex: 1,
  },
  causeItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  causeBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.accent1,
    marginRight: 12,
    marginTop: 7,
  },
  causeText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    flex: 1,
  },
});
